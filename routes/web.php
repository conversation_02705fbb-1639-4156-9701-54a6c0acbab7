<?php

use App\Http\Controllers\ProductController;
use App\Http\Controllers\PromotionController;
use Illuminate\Support\Facades\Route;


/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/


Route::get('/pp/{id}', [ProductController::class, 'upSellProducts']);
Route::get('/tpc-track', fn() => redirect()->to('https://www.tpcindia.com'));

Route::get('/promotion/picture/{key}', [PromotionController::class, 'promotionPicture']);

Route::post('/search-tap/move-to-wish-list/{product_id}', [ProductController::class, 'moveToWishList']);

Route::post('/admin/sales/delivered/update/{order_id}', [ProductController::class, 'updateOrderDelivered'])
->middleware(['admin.web.auth'])
->name('admin.sales.delivered.update');
