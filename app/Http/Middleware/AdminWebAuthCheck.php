<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AdminWebAuthCheck
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        // Check if admin is logged in via 'admin' guard
        if (!Auth::guard('admin')->check()) {
            return redirect()->route('admin.session.create'); // change this if your admin login route is different
        }

        return $next($request);
    }
}
