<?php

namespace App\Http\Controllers;

use App\Jobs\sendDeliveredSMSJob;
use App\Models\ProductInventoryIndice;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Webkul\Shop\Http\Resources\ProductResource;
use Illuminate\Http\Resources\Json\JsonResource;
use Webkul\Product\Models\ProductFlat;
use Webkul\Product\Repositories\ProductRepository;
use Webkul\Sales\Models\Order;
use Webkul\Shop\Http\Controllers\API\WishlistController;

class ProductController extends Controller
{

    /**
     * Create a controller instance.
     *
     * @return void
     */
    public function __construct(
        protected ProductRepository $productRepository
    ) {}


    /**
     * Up-sell product listings.
     *
     * @param  int  $id
     */
    public function upSellProducts($id): JsonResource
    {

        // $categories = HomeFlashSale::with('category')
        //     ->where('published', 1)
        //     ->when(! is_null($text), function ($q) use ($text) {
        //         $q->where('text', $text);
        //     })
        //     ->get();


        $product = $this->productRepository->findOrFail($id);

        $upSellProducts = $product->up_sells()
            ->take(core()->getConfigData('catalog.products.product_view_page.no_of_up_sells_products'))
            ->get();
        dd($upSellProducts[0]);
        dd((new ProductResource($upSellProducts[0]))->resolve());

        return ProductResource::collection($upSellProducts);
    }

    public function moveToWishList($product_id): JsonResponse
    {
        if (!auth()->guard('customer')->check()) {
            return response()->json([
                'status'     => 'unauthorized',
                'message'    => 'Please login as a customer to add products to wishlist.',
                'login_url'  => config('app.url') . '/customer/login',
            ], 401);
        }
        try {

            $product = ProductFlat::where('product_id', $product_id)->first();
            if (!$product) {
                return response()->json([
                    'status'  => 'error',
                    'message' => 'Invalid product ID.',
                ], 404);
            }

            request()->merge([
                'product_id' => $product_id,
            ]);
            $wishlistController = app(WishlistController::class);
            $response = $wishlistController->store();

            return $response->response()->setStatusCode(200);
        } catch (\Exception $e) {
            return response()->json([
                'status'  => 'error',
                'message' => 'Could not move product to wishlist.',
                'error'   => $e->getMessage(),
            ], 500);
        }
    }

    public function updateOrderDelivered ($order_id) {
        $order = Order::find($order_id);
        if($order) {
            $order->comments()->create([
                'comment'   => 'Delivered: ' . request()->input('comment'),
                'creaated_at'   =>  Carbon::now()
            ]);
            $order->status = 'delivered';
            $order->save();
            sendDeliveredSMSJob::dispatch($order_id);
        }
        return redirect()->back();
    }
}
