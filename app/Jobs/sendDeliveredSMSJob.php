<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Webkul\Sales\Models\Order;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;


class sendDeliveredSMSJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $order_id;

    /**
     * Create a new job instance.
     */
    public function __construct($orderId)
    {
        $this->order_id = $orderId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $order = Order::find($this->order_id);

        if (!$order) {
            Log::error("Order not found: {$this->order_id}");
            return;
        }

        // Fetch Mobile Number and Order Details
        $mobile = $order->billing_address->phone; // Adjust based on your database structure
        $message = "Order Delivered!\nDear Customer,\nYour order#". $this->order_id ." has been successfully delivered! Thankyou for Shopping with us.\nVIVEKS";
        $templateId = '1107173934705679234'; // Replace with your actual template ID

        // Get credentials from .env
        $user = config('services.cell24x7.user');
        $password = config('services.cell24x7.pwd');
        $sender = config('services.cell24x7.sender');

        // Prepare API URL
        $url = "https://sms.cell24x7.in/smsReceiver/sendSMS";

        // Send SMS
        $response = Http::get($url, [
            'user' => $user,
            'pwd' => $password,
            'sender' => $sender,
            'mobile' => $mobile,
            'msg' => $message,
            'mt' => 0,
            'tempId' => $templateId,
        ]);

        if ($response->successful()) {
            Log::info("SMS sent successfully for order #{$this->order_id} to {$mobile}");
        } else {
            Log::error("Failed to send SMS for order #{$this->order_id}: " . $response->body());
        }

    }
}
