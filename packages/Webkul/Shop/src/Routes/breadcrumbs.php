<?php

use Diglactic\Breadcrumbs\Breadcrumbs;
use Diglactic\Breadcrumbs\Generator as BreadcrumbTrail;

/**
 * Profile routes.
 */
Breadcrumbs::for('shop.customers.account.profile.index', function (BreadcrumbTrail $trail) {
    $trail->push(trans('shop::app.customers.account.profile.index.title'), route('shop.customers.account.profile.index'));
});

Breadcrumbs::for('shop.customers.account.profile.edit', function (BreadcrumbTrail $trail) {
    $trail->parent('shop.customers.account.profile.index');
});

/**
 * Order routes.
 */
Breadcrumbs::for('shop.customers.account.orders.index', function (BreadcrumbTrail $trail) {
    $trail->parent('shop.customers.account.profile.index');

    $trail->push(trans('shop::app.customers.account.orders.index.page-title'), route('shop.customers.account.orders.index'));
});

Breadcrumbs::for('shop.customers.account.orders.view', function (BreadcrumbTrail $trail, $id) {
    $trail->parent('shop.customers.account.orders.index');
});

/**
 * Downloadable products.
 */
Breadcrumbs::for('shop.customers.account.downloadable_products.index', function (BreadcrumbTrail $trail) {
    $trail->parent('shop.customers.account.profile.index');

    $trail->push(trans('shop::app.customers.account.downloadable_products.title'), route('shop.customers.account.downloadable_products.index'));
});

/**
 * Wishlists.
 */
Breadcrumbs::for('shop.customers.account.wishlist.index', function (BreadcrumbTrail $trail) {
    $trail->parent('shop.customers.account.profile.index');

    $trail->push(trans('shop::app.customers.account.wishlist.page-title'), route('shop.customers.account.wishlist.index'));
});

/**
 * Invoices.
 */
Breadcrumbs::for('shop.customers.account.invoices.index', function (BreadcrumbTrail $trail) {
    $trail->parent('shop.customers.account.profile.index');

    $trail->push(trans('shop::app.customers.account.invoices.page-title'), route('shop.customers.account.invoices.index'));
});

/**
 * Reviews.
 */
Breadcrumbs::for('shop.customers.account.reviews.index', function (BreadcrumbTrail $trail) {
    $trail->parent('shop.customers.account.profile.index');

    $trail->push(trans('shop::app.customers.account.reviews.index.page-title'), route('shop.customers.account.reviews.index'));
});

/**
 * Addresses.
 */
Breadcrumbs::for('shop.customers.account.addresses.index', function (BreadcrumbTrail $trail) {
    $trail->parent('shop.customers.account.profile.index');

    $trail->push(trans('shop::app.customers.account.addresses.index.page-title'), route('shop.customers.account.addresses.index'));
});

Breadcrumbs::for('shop.customers.account.addresses.create', function (BreadcrumbTrail $trail) {
    $trail->parent('shop.customers.account.addresses.index');

    $trail->push(trans('shop::app.customers.account.addresses.create.page-title'), route('shop.customers.account.addresses.create'));
});

Breadcrumbs::for('shop.customers.account.addresses.edit', function (BreadcrumbTrail $trail, $id) {
    $trail->parent('shop.customers.account.addresses.index');

    $trail->push(trans('shop::app.customers.account.addresses.edit.page-title'), route('shop.customers.account.addresses.edit', $id));
});
