<x-shop::layouts.account>
    <!-- Page Title -->
    <x-slot:title>
        @lang('shop::app.customers.account.invoices.page-title')
        </x-slot>

        <!-- Breadcrumbs -->
        @if ((core()->getConfigData('general.general.breadcrumbs.shop')))
        @section('breadcrumbs')
        <x-shop::breadcrumbs name="shop.customers.account.invoices.index" />
        @endSection
        @endif

        <div class="max-md:hidden">
            <x-shop::layouts.account.navigation />
        </div>

        <div class="mx-4 flex-auto">
            <!-- Invoices Content -->
            <div class="flex items-center justify-between">
                <div class="grid gap-1.5">
                    <h2 class="text-2xl font-medium max-md:text-xl">
                        @lang('shop::app.customers.account.invoices.page-title')
                    </h2>

                    <p class="text-gray-600">
                        @lang('shop::app.customers.account.invoices.description')
                    </p>
                </div>
            </div>

            <!-- Empty Invoices Content -->
            <div class="m-auto grid w-full place-content-center items-center justify-items-center py-32 text-center">
                <div
                    class="flex h-24 w-24 items-center justify-center rounded-full bg-gray-100 max-md:h-16 max-md:w-16">
                    <span class="icon-invoice text-4xl text-gray-400 max-md:text-3xl"></span>
                </div>

                <p class="text-xl max-md:text-sm mt-4" role="heading">
                    @lang('shop::app.customers.account.invoices.empty')
                </p>

                <p class="text-gray-600 mt-2">
                    @lang('shop::app.customers.account.invoices.empty-description')
                </p>
            </div>
        </div>
</x-shop::layouts.account>