<x-shop::layouts.account>
    <!-- Page Title -->
    <x-slot:title>
        @lang('shop::app.customers.account.invoices.page-title')
        </x-slot>

        <!-- Breadcrumbs -->
        @if ((core()->getConfigData('general.general.breadcrumbs.shop')))
        @section('breadcrumbs')
        <x-shop::breadcrumbs name="shop.customers.account.invoices.index" />
        @endSection
        @endif

        <div class="max-md:hidden">
            <x-shop::layouts.account.navigation />
        </div>

        <div class="mx-4 flex-auto">
            <!-- SAP Invoices Vue Component -->
            @auth('customer')
                <sap-invoice :customer='@json(auth()->guard('customer')->user())'>
                    <!-- Shimmer/Loading placeholder -->
                    <div class="animate-pulse">
                        <div class="flex items-center justify-between mb-6">
                            <div class="grid gap-1.5">
                                <div class="h-8 bg-gray-200 rounded w-48"></div>
                                <div class="h-4 bg-gray-200 rounded w-64"></div>
                            </div>
                        </div>
                        <div class="space-y-4">
                            <div class="h-12 bg-gray-200 rounded"></div>
                            <div class="h-12 bg-gray-200 rounded"></div>
                            <div class="h-12 bg-gray-200 rounded"></div>
                        </div>
                    </div>
                </sap-invoice>
            @else
                <div class="text-center py-32">
                    <p class="text-xl text-gray-600">Please log in to view your invoices.</p>
                </div>
            @endauth
        </div>
</x-shop::layouts.account>