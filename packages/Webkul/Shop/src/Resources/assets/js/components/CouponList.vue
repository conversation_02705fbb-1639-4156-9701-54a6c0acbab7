<template>
  <div class="coupon-container">
    <div class="coupon-header">
      <h2>Available Coupons</h2>
    </div>
    
    <div v-if="loading" class="skeleton-container">
      <div v-for="i in 3" :key="i" class="coupon-card skeleton">
        <div class="coupon-info">
          <div class="skeleton-title shimmer"></div>
          <div class="skeleton-description shimmer"></div>
          <div class="skeleton-validity shimmer"></div>
        </div>
        <div class="coupon-action">
          <div class="skeleton-button shimmer"></div>
        </div>
      </div>
    </div>
    
    <div v-else-if="error" class="error-state">
      <p>{{ error }}</p>
      <button @click="fetchCoupons" class="retry-button">Retry</button>
    </div>
    
    <div v-else-if="coupons.length === 0" class="empty-state">
      <p>No coupons available at the moment.</p>
    </div>
    
    <div v-else class="coupon-list">
      <div v-for="(coupon, index) in coupons" :key="index" class="coupon-card">
        <div class="coupon-info">
          <h3>{{ coupon.name }}</h3>
          <p class="coupon-description">{{ coupon.description }}</p>
          <p class="coupon-validity">Valid till: {{ formatDate(coupon.ends_till) }}</p>
        </div>
        <div class="coupon-action">
          <button 
            @click="copyCoupon(coupon.coupon_code)" 
            class="copy-button"
            :class="{ 'copied': copiedCode === coupon.coupon_code }"
          >
            {{ copiedCode === coupon.coupon_code ? 'Copied!' : 'Copy Code' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CouponList',
  data() {
    return {
      coupons: [],
      loading: true,
      error: null,
      copiedCode: null
    }
  },
  mounted() {
    this.fetchCoupons();
  },
  methods: {
    async fetchCoupons() {
      this.loading = true;
      this.error = null;
      
      try {
        const response = await fetch('/api/checkout-coupons/active');
        
        if (!response.ok) {
          throw new Error(`API error: ${response.status}`);
        }
        
        const result = await response.json();
        this.coupons = result.data;
      } catch (error) {
        console.error('Error fetching coupons:', error);
        this.error = 'Failed to load coupons. Please try again.';
      } finally {
        this.loading = false;
      }
    },
    
    formatDate(dateString) {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric' 
      });
    },
    
    copyCoupon(code) {
      navigator.clipboard.writeText(code)
        .then(() => {
          this.copiedCode = code;
          setTimeout(() => {
            if (this.copiedCode === code) {
              this.copiedCode = null;
            }
          }, 2000);
        })
        .catch(err => {
          console.error('Failed to copy code:', err);
        });
    }
  }
}
</script>

<style scoped>
.coupon-container {
  font-family: Arial, sans-serif;
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.coupon-header {
  margin-bottom: 20px;
}

.coupon-header h2 {
  font-size: 24px;
  color: #333;
  margin: 0;
}

.coupon-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.coupon-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.coupon-info {
  flex: 1;
}

.coupon-info h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #333;
}

.coupon-description {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 14px;
}

.coupon-validity {
  margin: 0;
  color: #888;
  font-size: 12px;
}

.coupon-action {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.coupon-code {
  padding: 8px 12px;
  background-color: #f5f5f5;
  border: 1px dashed #ccc;
  border-radius: 4px;
  font-weight: bold;
  color: #333;
  font-size: 14px;
}

.copy-button {
  padding: 8px 16px;
  background-color: #C41230;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
  min-width: 100px;
}

.copy-button:hover {
  background-color: #a50f28;
}

.copy-button.copied {
  background-color: #4CAF50;
}

.loading-state, .error-state, .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  text-align: center;
  color: #666;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #C41230;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.retry-button {
  margin-top: 15px;
  padding: 8px 16px;
  background-color: #C41230;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.skeleton-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.coupon-card.skeleton {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #f5f5f5;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.coupon-info {
  flex: 1;
}

.skeleton-title {
  width: 70%;
  height: 16px;
  margin-bottom: 10px;
}

.skeleton-description {
  width: 80%;
  height: 12px;
  margin-bottom: 10px;
}

.skeleton-validity {
  width: 50%;
  height: 10px;
}

.coupon-action {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.skeleton-button {
  width: 100px;
  height: 30px;
}

.shimmer {
  background: linear-gradient(90deg, #f5f5f5, #e0e0e0, #f5f5f5);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -100%;
  }
  100% {
    background-position: 100%;
  }
}
</style>
