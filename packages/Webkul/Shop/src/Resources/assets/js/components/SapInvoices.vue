<template>
  <div class="sap-invoices-container">
    <div class="invoices-header">
      <h2>SAP Invoices</h2>
      <div class="date-filters">
        <div class="date-input-group">
          <label for="fromDate">From Date:</label>
          <input id="fromDate" type="date" v-model="fromDate" class="date-input" />
        </div>
        <div class="date-input-group">
          <label for="toDate">To Date:</label>
          <input id="toDate" type="date" v-model="toDate" class="date-input" />
        </div>
        <button @click="fetchInvoices" class="submit-button" :disabled="loading">
          {{ loading ? 'Loading...' : 'Submit' }}
        </button>
      </div>
    </div>

    <div v-if="loading" class="loading-state">
      <div class="spinner"></div>
      <p>Loading invoices...</p>
    </div>

    <div v-else-if="error" class="error-state">
      <p>{{ error }}</p>
      <button @click="fetchInvoices" class="retry-button">Retry</button>
    </div>

    <div v-else-if="invoices.length === 0 && hasSearched" class="empty-state">
      <div class="text-center space-y-4">
        <p class="font-bold text-orange-600 bg-orange-50 border border-orange-200 rounded-lg p-4">
          ⚠️Invoice not found.Please log in with your registered mobile number used during purchase to download the
          invoice.
        </p>
      </div>
    </div>

    <div v-else-if="invoices.length > 0" class="invoices-list">
      <div v-for="(invoice, index) in invoices" :key="index" class="invoice-card">
        <div class="invoice-info">
          <h3>Invoice #{{ invoice.DocNum }}</h3>
          <p class="invoice-date">{{ formatInvoiceDate(invoice.DocDate) }}</p>
        </div>
        <div class="invoice-action">
          <button @click="downloadInvoice(invoice.Link)" class="download-button">
            <svg class="download-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
              <polyline points="7,10 12,15 17,10" />
              <line x1="12" y1="15" x2="12" y2="3" />
            </svg>
            Download
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SapInvoices',
  props: {
    customer: {
      type: Object,
      required: false,
      default: () => ({}),
      validator(value) {
        return !value || typeof value === 'object';
      }
    }
  },
  data() {
    return {
      invoices: [],
      loading: false,
      error: null,
      hasSearched: false,
      fromDate: this.getDefaultFromDate(),
      toDate: this.getDefaultToDate()
    }
  },

  mounted() {
    // Debug: Print customer data when component mounts
    console.log('SapInvoices component mounted');
    console.log('Customer prop on mount:', this.customer);
    console.log('Customer type on mount:', typeof this.customer);
  },
  methods: {
    getDefaultFromDate() {
      const today = new Date();
      const sixMonthsAgo = new Date(today);
      sixMonthsAgo.setMonth(today.getMonth() - 6);

      // Handle edge case where current month has more days than target month
      if (sixMonthsAgo.getDate() !== today.getDate()) {
        sixMonthsAgo.setDate(0); // Set to last day of previous month
      }

      return sixMonthsAgo.toISOString().split('T')[0];
    },

    getDefaultToDate() {
      const today = new Date();
      return today.toISOString().split('T')[0];
    },

    async fetchInvoices() {
      // Debug: Print customer data
      console.log('Customer prop received:', this.customer);
      console.log('Customer phone:', this.customer?.phone);
      console.log('Customer data type:', typeof this.customer);
      console.log('All customer properties:', Object.keys(this.customer || {}));
      // if (!this.customer?.phone) {
      //   this.error = 'Customer phone number is required';
      //   return;
      // }

      if (!this.fromDate || !this.toDate) {
        this.error = 'Please select both from and to dates';
        return;
      }

      if (new Date(this.fromDate) > new Date(this.toDate)) {
        this.error = 'From date cannot be later than to date';
        return;
      }

      this.loading = true;
      this.error = null;
      this.hasSearched = true;

      try {
        const params = new URLSearchParams({
          from_date: this.fromDate,
          to_date: this.toDate,
          mobile_no: encodeURIComponent("8754483081")
        });

        const response = await fetch(`/api/fetch-sap-invoice?${params}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          }
        });

        if (!response.ok) {
          throw new Error(`API error: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();

        if (result.status === 'success') {
          this.invoices = result.data || [];
        } else {
          throw new Error(result.message || 'Failed to fetch invoices');
        }
      } catch (error) {
        console.error('Error fetching invoices:', error);
        this.error = error.message || 'Failed to load invoices. Please try again.';
        this.invoices = [];
      } finally {
        this.loading = false;
      }
    },

    formatInvoiceDate(docNum) {
      // Extract date from document number if it contains date info
      // This is a placeholder - adjust based on your actual date format
      return doc.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    },

    downloadInvoice(link) {
      if (link) {
        // Open the invoice link in a new tab
        window.open(link, '_blank');
      }
    }
  }
}
</script>

<style scoped>
.sap-invoices-container {
  font-family: Arial, sans-serif;
  max-width: 800px;
  padding: 20px;
}

.invoices-header {
  margin-bottom: 20px;
}

.invoices-header h2 {
  font-size: 24px;
  color: #333;
  margin: 0 0 15px 0;
}

.date-filters {
  display: flex;
  gap: 15px;
  align-items: end;
  flex-wrap: wrap;
}

.date-input-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.date-input-group label {
  font-size: 14px;
  color: #555;
  font-weight: 500;
}

.date-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  min-width: 150px;
}

.date-input:focus {
  outline: none;
  border-color: #C41230;
  box-shadow: 0 0 0 2px rgba(196, 18, 48, 0.1);
}

.submit-button {
  padding: 8px 20px;
  background-color: #C41230;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
  height: fit-content;
}

.submit-button:hover:not(:disabled) {
  background-color: #a50f28;
}

.submit-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.invoices-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.invoice-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.invoice-info {
  flex: 1;
}

.invoice-info h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #333;
}

.invoice-date {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.invoice-action {
  display: flex;
  align-items: center;
}

.download-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #C41230;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.download-button:hover {
  background-color: #a50f28;
}

.download-icon {
  width: 16px;
  height: 16px;
}

.loading-state,
.error-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  text-align: center;
  color: #666;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #C41230;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.retry-button {
  margin-top: 15px;
  padding: 8px 16px;
  background-color: #C41230;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.retry-button:hover {
  background-color: #a50f28;
}

@media (max-width: 768px) {
  .date-filters {
    flex-direction: column;
    align-items: stretch;
  }

  .date-input {
    min-width: auto;
  }

  .invoice-card {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .invoice-action {
    justify-content: center;
  }
}
</style>
