/**
 * This will track all the images and fonts for publishing.
 */
import.meta.glob(["../images/**", "../fonts/**"]);

/**
 * Main vue bundler.
 */
import { createApp } from "vue/dist/vue.esm-bundler";

/**
 * Main root application registry.
 */
window.app = createApp({
    data() {
        return {
            accordions: {},
            storeLocatorSearch: '',
            leftColumnJobs: [],
            rightColumnJobs: [],
            position: "",
            showSearch: false
        };
    },

    mounted() {
        this.lazyImages();
    },

    methods: {
        onSubmit() { },

        onInvalidSubmit() { },

        lazyImages() {
            var lazyImages = [].slice.call(
                document.querySelectorAll("img.lazy")
            );

            let lazyImageObserver = new IntersectionObserver(function (
                entries,
                observer
            ) {
                entries.forEach(function (entry) {
                    if (entry.isIntersecting) {
                        let lazyImage = entry.target;

                        lazyImage.src = lazyImage.dataset.src;

                        lazyImage.classList.remove("lazy");

                        lazyImageObserver.unobserve(lazyImage);
                    }
                });
            });

            lazyImages.forEach(function (lazyImage) {
                lazyImageObserver.observe(lazyImage);
            });
        },

        toggleVisibility(section, targetClass, sectionRef) {
            console.log(section, targetClass);
            // if (this.$refs[sectionRef] && this.$refs[sectionRef].splide) {
            //     this.$refs[sectionRef].splide.destroy(true)
            // }

            // Select all categories within the specified instance
            const categoriesTab = document.querySelectorAll(`.${section} .tabs a`);

            // Hide all categories in this instance
            categoriesTab.forEach((category) => {
                category.classList.add("inactive-tab");
                category.classList.remove("active-tab");
            });

            // Show the target category
            const targetTab = document.querySelectorAll(`.${section} .tabs a.${targetClass}`);

            targetTab.forEach((category) => {
                category.classList.remove("inactive-tab");
                category.classList.add("active-tab");
            });


            // toggle products

            // Select all categories within the specified instance
            const categories = document.querySelectorAll(`.${section} .tab-content .all`);

            // Hide all categories in this instance
            categories.forEach((category) => {
                category.classList.add("hidden");
            });

            // Show the target category
            const target = document.querySelectorAll(`.${section} .tab-content .${targetClass}`);

            target.forEach((category) => {
                category.classList.remove("hidden");

            });


            //shop all
            // Select the single element with the class 'shop-all' within the specified section
            const shopAllLink = document.querySelector(`.${section} .shop-all`);

            // Check if the element exists before modifying the href attribute
            if (shopAllLink) {
                // Set the new href attribute
                shopAllLink.href = '/' + targetClass; // Replace 'new-url.html' with your desired URL
            }

            if (this.$refs[sectionRef] && this.$refs[sectionRef].splide) {
                this.$refs[sectionRef].splide.go(0);
            }


        },



        // Toggle accordion
        toggle(accordionId) {
            this.accordions[accordionId] = !this.accordions[accordionId];
        },

        // Check if an accordion is open
        isOpen(accordionId) {
            return this.accordions[accordionId];
        },

        toggleAnswer(index, column) {
            const arrayName = `${column}ColumnJobs`;
            if (this[arrayName] && this[arrayName][index]) {
                this[arrayName][index].isOpen = !this[arrayName][index].isOpen;
            } else {
                this[arrayName][index] = {
                    isOpen: true
                };
            }
        }
    },
});


/**
 * Global plugins registration.
 */
import Axios from "./plugins/axios";
import Emitter from "./plugins/emitter";
import Flatpickr from "./plugins/flatpickr";
import Shop from "./plugins/shop";
import VeeValidate from "./plugins/vee-validate";
import PrimeVue from 'primevue/config';
import Lara from '@primeuix/themes/material';

[Axios, Emitter, Shop, VeeValidate, Flatpickr].forEach((plugin) =>
    app.use(plugin)
);

app.use(PrimeVue, {
    theme: {
        preset: Lara, options: {
            darkModeSelector: false, // forces light mode
            colorScheme: 'light-blue' // optional: 'light-blue', 'light-green', etc.
        }
    }
});

/**
 * Import VOtpInput for Vue
 */
import VOtpInput from "vue3-otp-input";
app.component('v-otp-input', VOtpInput)
/**
 * Import Splide for Vue
 */
import '@splidejs/splide/css/sea-green';
import { Splide, SplideSlide } from '@splidejs/vue-splide';
/**
 * Register Splide globally
 */
app.component('Splide', Splide);
app.component('SplideSlide', SplideSlide);
app.component('SplideTrack', SplideSlide);
/**
 * Import and Register the GoogleMap Component
 */
import GoogleMapComponent from './components/GoogleMapComponent.vue';
import StoreSearch from './components/StoreSearch.vue';
app.component('store-search', StoreSearch);
app.component('google-map-component', GoogleMapComponent);
// accordion
import FaqAccordion from './components/FaqAccordion.vue';
app.component('faq-accordion', FaqAccordion);

import ExtendedWarranty from './components/ExtendedWarranty.vue';
import ExchangeModule from './components/ExchangeModule.vue'
import ProductAddToCart from './components/ProductAddToCart.vue';
import ProductAddToCartPdp from './components/ProductAddToCartPdp.vue';
import ProductAddToWishList from './components/ProductAddToWishList.vue';
import SearchTap from './components/SearchTap.vue';
import CouponList from "./components/CouponList.vue";
import SapInvoices from "./components/SapInvoices.vue";
app.component('add-to-cart', ProductAddToCart);
app.component('add-to-cart-pdp', ProductAddToCartPdp);
app.component('add-to-wish-list', ProductAddToWishList);
app.component('search-tap', SearchTap);
app.component('extended-warranty', ExtendedWarranty);
app.component('exchange-button', ExchangeModule)
app.component('coupon-list', CouponList)
app.component('sap-invoice', SapInvoices)


export default app;
